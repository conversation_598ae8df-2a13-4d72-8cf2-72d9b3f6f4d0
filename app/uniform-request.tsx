import {
  Form<PERSON>elect,
  FormSubmitButton,
  FormQuantitySelector,
} from "@/components/forms";
import { Colors } from "@/constants/Colors";
import { zodResolver } from "@hookform/resolvers/zod";
import { StatusBar } from "expo-status-bar";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useFieldArray, useForm, useWatch } from "react-hook-form";
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  View,
} from "react-native";
import { z } from "zod";

import {
  useCreateInventoryRequestMutation,
  useGetInventoryQuery,
} from "@/generated/graphql";
import { useSession } from "@/providers/auth-provider";

// SKU Generator function
const generateSKU = (itemName: string, attributeValues: string[]): string => {
  // Create SKU by combining item name with attribute values
  return `${itemName} - ${attributeValues
    .filter(Boolean)
    .join("/")}`.toUpperCase();
};

interface InventoryItem {
  item: string;
  quantity: number;
  sku: string;
  costPrice: number;
  sellingPrice: number;
}

interface Attribute {
  attibuteName: string;
  attributeValues: string[];
}

interface InventoryProduct {
  _id: string;
  id: string;
  createdAt: string;
  updatedAt: string;
  item: string;
  description: string;
  type: string;
  items: InventoryItem[];
  attributes: Attribute[];
}

interface DropdownOption {
  label: string;
  value: string;
}

export default function UniformRequestScreen() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { data: inventoryData, isLoading } = useGetInventoryQuery(undefined, {
    initialData: { inventory: [] },
  });
  const { mutateAsync: requestFromInventory } =
    useCreateInventoryRequestMutation();

  // Create inventory options from inventory items
  const inventoryOptions = useMemo(() => {
    return (
      inventoryData?.inventory?.map((item) => ({
        label: item.item,
        value: item._id,
      })) || []
    );
  }, [inventoryData]);

  // Schema for uniform request form
  const uniformRequestSchema = z.object({
    inventory: z.string().min(1, "Inventory is required"),
    quantity: z.coerce.number().min(1, "Quantity must be at least 1"),
    selectedAttributes: z.array(
      z.object({
        attributeName: z.string().min(1, "Attribute name is required"),
        value: z.string().min(1, "Attribute value is required"),
      })
    ),
  });

  type UniformRequestFormData = z.infer<typeof uniformRequestSchema>;

  // Default values for uniform request form
  const defaultUniformRequestValues: UniformRequestFormData = {
    inventory: "",
    quantity: 1,
    selectedAttributes: [],
  };

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    watch,
    formState: { isValid, errors },
  } = useForm<UniformRequestFormData>({
    defaultValues: defaultUniformRequestValues,
    resolver: zodResolver(uniformRequestSchema),
    mode: "onChange",
  });
  const { mutateAsync: createInventoryRequest } =
    useCreateInventoryRequestMutation();

  const [attrsRecord, setAttrsRecord] = useState<Record<string, Attribute[]>>(
    {}
  );

  const getAttributes = useCallback(
    (inventoryId: string) => {
      return (
        inventoryData?.inventory?.find((item) => item._id === inventoryId)
          ?.attributes || []
      );
    },
    [inventoryData]
  );

  const { replace: replaceAttrs, fields } = useFieldArray({
    control,
    name: "selectedAttributes",
  });

  // manage attrs
  useEffect(() => {
    const subscription = watch((v, { name, type, values }) => {
      if (name === "inventory") {
        setAttrsRecord({});

        const attrs = getAttributes(v.inventory!);

        setAttrsRecord(() => ({
          [v.inventory!]: attrs,
        }));

        replaceAttrs(
          attrs.map((a) => ({ attributeName: a.attibuteName, value: "" }))
        );
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, inventoryData]);

  const session = useSession();

  // Handle form submission
  const onSubmit = async (data: UniformRequestFormData) => {
    try {
      setIsSubmitting(true);
      // await requestFromInventory({ input: data });
      // Handle success (e.g., show success message, navigate back)
      const inventory = inventoryData?.inventory?.find(
        (item) => item._id === data.inventory
      );

      if (!inventory) {
        throw new Error("Inventory not found");
      }

      const sku = generateSKU(
        inventory.item,
        data.selectedAttributes.map((a) => a.value)
      );

      return console.log(sku);

      await createInventoryRequest({
        input: {
          inventory: data.inventory,
          items: [
            {
              item: inventory.item,
              sku,
              quantity: data.quantity,
              selectedAttributes: data.selectedAttributes,
            },
          ],
          requestedBy: session.session?.userId!,
        },
      });
    } catch (error) {
      console.error("Error submitting request:", error);
      // Handle error (e.g., show error message)
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <View style={styles.flex}>
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        keyboardVerticalOffset={100}
        style={styles.container}
      >
        <ScrollView
          contentContainerStyle={styles.flex}
          showsVerticalScrollIndicator={false}
        >
          {/* Inventory Dropdown */}
          <FormSelect
            name="inventory"
            control={control}
            label="Select Inventory"
            placeholder="Select Inventory"
            options={inventoryOptions}
          />

          {/* show attrs */}
          {fields.map(({ id, attributeName }, idx) => {
            return (
              <FormSelect
                key={id}
                name={`selectedAttributes.${idx}.value`}
                control={control}
                label={attributeName}
                placeholder="Enter value"
                options={
                  attrsRecord[watch("inventory")!]?.[idx]?.attributeValues.map(
                    (v) => ({ label: v, value: v })
                  ) || []
                }
              />
            );
          })}

          {/* Item Quantity */}
          <FormQuantitySelector
            name="quantity"
            control={control}
            label="Quantity"
            min={1}
            max={100}
          />

          {/* Submit Button */}
          <FormSubmitButton
            submitLabel="Request Gear"
            onSubmit={handleSubmit(onSubmit)}
            isValid={isValid}
            isSubmitting={isSubmitting}
            style={styles.submitButton}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingBottom: 50,
    paddingTop: 16,
    paddingHorizontal: 16,
  },
  submitButton: {
    marginTop: 20,
  },
  flex: { flex: 1 },
});
